# 黔东南州非物质文化遗产爬虫程序

## 项目简介

本项目是一个专门用于爬取贵州省黔东南州非物质文化遗产信息的爬虫程序。程序从官方权威网站获取数据，确保信息的真实性和可靠性。

## 特点

- **权威数据源**: 仅从政府官方网站、文化部门官网等权威渠道获取数据
- **数据验证**: 内置数据验证和清洗机制，确保数据质量
- **多格式输出**: 支持CSV、JSON、Excel多种格式输出
- **智能去重**: 自动识别和过滤重复内容
- **详细日志**: 完整的爬取过程日志记录
- **可配置**: 支持自定义爬取参数和数据源

## 数据源

### 官方权威数据源
- 贵州省人民政府网 (http://www.guizhou.gov.cn/)
- 黔东南州人民政府网 (http://www.qdn.gov.cn/)
- 中华人民共和国文化和旅游部 (https://www.mct.gov.cn/)
- 中国非物质文化遗产网 (http://www.ihchina.cn/)

### 各县市政府网站
- 凯里市、从江县、榕江县、雷山县等16个县市政府官网

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
# 运行完整爬虫（推荐）
python run_crawler.py

# 仅爬取官方数据源
python run_crawler.py --mode official

# 仅爬取县市数据源
python run_crawler.py --mode county

# 自定义输出目录
python run_crawler.py --output-dir ./my_output

# 调整爬取参数
python run_crawler.py --max-pages 100 --delay 3
```

### 直接运行主程序

```bash
python qiandongnan_heritage_crawler.py
```

## 输出文件

程序会在输出目录生成以下文件：

- `qiandongnan_heritage_YYYYMMDD_HHMMSS.csv` - CSV格式数据
- `qiandongnan_heritage_YYYYMMDD_HHMMSS.json` - JSON格式数据  
- `qiandongnan_heritage_YYYYMMDD_HHMMSS.xlsx` - Excel格式数据（包含统计信息）
- `report_YYYYMMDD_HHMMSS.txt` - 爬取报告
- `heritage_crawler_YYYYMMDD_HHMMSS.log` - 详细日志

## 数据字段说明

| 字段名 | 说明 |
|--------|------|
| title | 非遗项目标题 |
| content | 详细内容描述 |
| source | 数据来源网站 |
| keyword | 搜索关键词 |
| link | 原文链接 |
| date | 发布日期 |
| crawl_time | 爬取时间 |
| validation_score | 数据可信度评分(0-100) |
| heritage_level | 遗产级别(national/provincial/local/unknown) |

## 配置说明

### 爬虫配置 (config.py)

```python
CRAWLER_CONFIG = {
    'request_delay': 2,      # 请求间隔（秒）
    'max_retries': 3,        # 最大重试次数
    'timeout': 10,           # 请求超时时间（秒）
    'max_pages_per_site': 50, # 每个网站最大爬取页面数
    'log_level': 'INFO'      # 日志级别
}
```

### 数据验证规则

- 最小内容长度: 50字符
- 必须包含关键词: ['非物质文化遗产', '非遗', '传统']
- 排除关键词: ['广告', '推广', '商业']
- 可信度评分机制: 基于来源权威性、内容质量等多维度评估

## 项目结构

```
CultrueCrawler/
├── qiandongnan_heritage_crawler.py  # 主爬虫程序
├── config.py                        # 配置文件
├── data_validator.py                # 数据验证模块
├── run_crawler.py                   # 运行脚本
├── requirements.txt                 # 依赖包列表
└── README.md                        # 说明文档
```

## 注意事项

1. **合法合规**: 本程序仅从公开的政府官方网站获取信息，遵守robots.txt协议
2. **请求频率**: 默认设置2秒请求间隔，避免对服务器造成压力
3. **数据真实性**: 所有数据均来自官方权威渠道，经过验证和清洗
4. **网络环境**: 需要稳定的网络连接，建议在网络状况良好时运行

## 常见问题

### Q: 为什么有些网站爬取不到数据？
A: 可能原因包括：
- 网站结构变化
- 网络连接问题
- 网站访问限制
- 内容不包含相关关键词

### Q: 如何提高数据质量？
A: 程序内置了多重验证机制：
- 关键词匹配验证
- 来源可信度评估
- 内容质量评分
- 重复内容过滤

### Q: 可以添加新的数据源吗？
A: 可以在config.py中添加新的官方数据源，确保来源的权威性和合法性。

## 技术支持

如有问题或建议，请查看日志文件获取详细错误信息。

## 免责声明

本程序仅用于学习和研究目的，所爬取的数据均来自公开的官方网站。使用者应遵守相关法律法规和网站使用条款。
