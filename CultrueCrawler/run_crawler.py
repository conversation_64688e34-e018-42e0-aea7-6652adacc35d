#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黔东南州非物质文化遗产爬虫运行脚本
"""

import sys
import os
import argparse
from datetime import datetime
from qiandongnan_heritage_crawler import QiandongnanHeritageCrawler

def main():
    parser = argparse.ArgumentParser(description='黔东南州非物质文化遗产爬虫')
    parser.add_argument('--mode', choices=['official', 'county', 'all'], default='all',
                       help='爬取模式: official(仅官方源), county(仅县市源), all(全部)')
    parser.add_argument('--output-dir', default='./output',
                       help='输出目录')
    parser.add_argument('--max-pages', type=int, default=50,
                       help='每个网站最大爬取页面数')
    parser.add_argument('--delay', type=int, default=2,
                       help='请求间隔时间(秒)')
    
    args = parser.parse_args()
    
    # 创建输出目录
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
        
    # 初始化爬虫
    crawler = QiandongnanHeritageCrawler()
    
    # 更新配置
    crawler.crawler_config['max_pages_per_site'] = args.max_pages
    crawler.crawler_config['request_delay'] = args.delay
    
    print("=" * 60)
    print("黔东南州非物质文化遗产爬虫")
    print("=" * 60)
    print(f"爬取模式: {args.mode}")
    print(f"输出目录: {args.output_dir}")
    print(f"最大页面数: {args.max_pages}")
    print(f"请求间隔: {args.delay}秒")
    print("=" * 60)
    
    try:
        # 根据模式运行爬虫
        if args.mode in ['official', 'all']:
            print("开始爬取官方权威数据源...")
            crawler.crawl_official_sources()
            
        if args.mode in ['county', 'all']:
            print("开始爬取各县市政府网站...")
            crawler.crawl_county_sources()
            
        # 数据验证和清洗
        print("开始数据验证和清洗...")
        cleaned_data = crawler.validator.clean_heritage_data(crawler.heritage_data)
        crawler.heritage_data = cleaned_data
        
        # 保存数据
        if crawler.heritage_data:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存到指定目录
            csv_file = os.path.join(args.output_dir, f'qiandongnan_heritage_{timestamp}.csv')
            json_file = os.path.join(args.output_dir, f'qiandongnan_heritage_{timestamp}.json')
            excel_file = os.path.join(args.output_dir, f'qiandongnan_heritage_{timestamp}.xlsx')
            
            crawler.save_to_csv(csv_file)
            crawler.save_to_json(json_file)
            crawler.save_to_excel(excel_file)
            
            # 生成报告
            crawler.generate_report(os.path.join(args.output_dir, f'report_{timestamp}.txt'))
            
            print(f"\n爬取完成！")
            print(f"共获取 {len(crawler.heritage_data)} 条有效数据")
            print(f"数据已保存到: {args.output_dir}")
            
        else:
            print("未获取到有效数据")
            
    except KeyboardInterrupt:
        print("\n用户中断爬取")
    except Exception as e:
        print(f"爬取过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
