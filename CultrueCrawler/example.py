#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黔东南州非物质文化遗产爬虫使用示例
"""

from qiandongnan_heritage_crawler import QiandongnanHeritageCrawler
from datetime import datetime
import os

def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建爬虫实例
    crawler = QiandongnanHeritageCrawler()
    
    # 设置较小的爬取量用于演示
    crawler.crawler_config['max_pages_per_site'] = 5
    crawler.crawler_config['request_delay'] = 1
    
    print("开始爬取官方数据源...")
    crawler.crawl_official_sources()
    
    print(f"原始数据: {len(crawler.heritage_data)} 条")
    
    # 数据验证和清洗
    if crawler.heritage_data:
        print("开始数据验证和清洗...")
        cleaned_data = crawler.validator.clean_heritage_data(crawler.heritage_data)
        crawler.heritage_data = cleaned_data
        
        print(f"清洗后数据: {len(crawler.heritage_data)} 条")
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = './example_output'
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        csv_file = os.path.join(output_dir, f'example_{timestamp}.csv')
        json_file = os.path.join(output_dir, f'example_{timestamp}.json')
        
        crawler.save_to_csv(csv_file)
        crawler.save_to_json(json_file)
        
        print(f"数据已保存到: {output_dir}")
        
        # 显示前几条数据
        print("\n=== 示例数据 ===")
        for i, item in enumerate(crawler.heritage_data[:3]):
            print(f"\n第{i+1}条:")
            print(f"标题: {item['title']}")
            print(f"来源: {item['source']}")
            print(f"级别: {item.get('heritage_level', 'unknown')}")
            print(f"评分: {item.get('validation_score', 0)}")
            print(f"内容: {item['content'][:100]}...")
    else:
        print("未获取到数据")

def example_county_only():
    """仅爬取县市数据源示例"""
    print("\n=== 县市数据源示例 ===")
    
    crawler = QiandongnanHeritageCrawler()
    crawler.crawler_config['max_pages_per_site'] = 3
    
    print("开始爬取县市数据源...")
    crawler.crawl_county_sources()
    
    print(f"获取数据: {len(crawler.heritage_data)} 条")

def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    crawler = QiandongnanHeritageCrawler()
    
    # 模拟一些示例数据
    sample_data = [
        {
            'title': '苗族古歌',
            'content': '苗族古歌是苗族人民世代传承的民间文学，记录了苗族的历史、文化和传统。在黔东南州雷山县等地广泛传承。',
            'source': '贵州省政府网',
            'heritage_level': 'national',
            'validation_score': 95
        },
        {
            'title': '侗族大歌',
            'content': '侗族大歌是侗族多声部民歌的统称，主要流传在黔东南州从江县、榕江县等侗族聚居区。',
            'source': '黔东南州政府网',
            'heritage_level': 'national', 
            'validation_score': 92
        },
        {
            'title': '苗族银饰锻制技艺',
            'content': '苗族银饰锻制技艺是苗族传统手工艺，在凯里市、雷山县等地有悠久历史。',
            'source': '中国非物质文化遗产网',
            'heritage_level': 'national',
            'validation_score': 88
        }
    ]
    
    crawler.heritage_data = sample_data
    
    # 生成统计信息
    stats = crawler.get_statistics_data()
    
    print("统计信息:")
    for stat in stats:
        print(f"{stat['类型']} - {stat['项目']}: {stat['数量']}")
    
    # 按评分排序
    sorted_data = sorted(crawler.heritage_data, key=lambda x: x['validation_score'], reverse=True)
    
    print("\n按评分排序的前3项:")
    for i, item in enumerate(sorted_data[:3]):
        print(f"{i+1}. {item['title']} (评分: {item['validation_score']})")

if __name__ == "__main__":
    print("黔东南州非物质文化遗产爬虫使用示例")
    print("=" * 50)
    
    try:
        # 运行基本示例
        example_basic_usage()
        
        # 运行县市示例
        example_county_only()
        
        # 运行数据分析示例
        example_data_analysis()
        
        print("\n示例运行完成！")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        print("请检查网络连接和依赖包安装情况")
