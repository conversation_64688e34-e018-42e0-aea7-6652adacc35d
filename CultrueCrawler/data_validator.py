#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证和清洗模块
确保爬取的非物质文化遗产数据真实可靠
"""

import re
import logging
from datetime import datetime
from urllib.parse import urlparse
from config import HERITAGE_KEYWORDS, VALIDATION_RULES

class HeritageDataValidator:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.heritage_keywords = HERITAGE_KEYWORDS
        self.validation_rules = VALIDATION_RULES
        
        # 已知的官方认证非遗项目（用于交叉验证）
        self.official_heritage_projects = {
            # 国家级非物质文化遗产代表性项目
            'national_level': [
                '苗族古歌', '侗族大歌', '苗族飞歌', '侗族琵琶歌',
                '苗族锦鸡舞', '侗族多耶', '苗族芦笙舞',
                '苗族银饰锻制技艺', '侗族木构建筑营造技艺',
                '苗族蜡染技艺', '苗族刺绣', '侗族刺绣',
                '苗族鼓藏节', '侗族萨玛节', '苗族姊妹节'
            ],
            
            # 省级非物质文化遗产代表性项目
            'provincial_level': [
                '苗族踩花山', '侗族林王节', '苗族跳月',
                '侗族弹唱', '苗族情歌', '侗族侗戏',
                '苗族酿酒技艺', '侗族腌鱼技艺',
                '苗医药', '侗医药', '苗族婚俗', '侗族婚俗'
            ]
        }
        
    def validate_heritage_item(self, item):
        """验证单个非遗数据项"""
        validation_result = {
            'is_valid': True,
            'confidence_score': 0,
            'issues': [],
            'heritage_level': 'unknown'
        }
        
        try:
            # 1. 基本内容验证
            if not self._validate_basic_content(item):
                validation_result['is_valid'] = False
                validation_result['issues'].append('基本内容验证失败')
                
            # 2. 关键词验证
            keyword_score = self._validate_keywords(item)
            validation_result['confidence_score'] += keyword_score
            
            # 3. 来源可信度验证
            source_score = self._validate_source(item)
            validation_result['confidence_score'] += source_score
            
            # 4. 项目级别识别
            heritage_level = self._identify_heritage_level(item)
            validation_result['heritage_level'] = heritage_level
            
            # 5. 内容质量评估
            quality_score = self._assess_content_quality(item)
            validation_result['confidence_score'] += quality_score
            
            # 6. 时间有效性验证
            if not self._validate_date(item):
                validation_result['issues'].append('日期格式异常')
                
            # 7. 去重验证
            if self._is_duplicate_content(item):
                validation_result['issues'].append('疑似重复内容')
                
            # 最终评分
            validation_result['confidence_score'] = min(100, validation_result['confidence_score'])
            
            # 如果置信度过低，标记为无效
            if validation_result['confidence_score'] < 30:
                validation_result['is_valid'] = False
                validation_result['issues'].append('置信度过低')
                
        except Exception as e:
            self.logger.error(f"验证数据时出错: {e}")
            validation_result['is_valid'] = False
            validation_result['issues'].append(f'验证过程异常: {str(e)}')
            
        return validation_result
        
    def _validate_basic_content(self, item):
        """基本内容验证"""
        # 检查必要字段
        required_fields = ['title', 'content', 'source']
        for field in required_fields:
            if not item.get(field) or not item[field].strip():
                return False
                
        # 检查内容长度
        content = item.get('content', '')
        if len(content) < self.validation_rules['min_content_length']:
            return False
            
        # 检查是否包含必要关键词
        content_lower = content.lower()
        title_lower = item.get('title', '').lower()
        combined_text = content_lower + ' ' + title_lower
        
        required_keywords = self.validation_rules['required_keywords']
        if not any(keyword in combined_text for keyword in required_keywords):
            return False
            
        # 检查是否包含排除关键词
        exclude_keywords = self.validation_rules['exclude_keywords']
        if any(keyword in combined_text for keyword in exclude_keywords):
            return False
            
        return True
        
    def _validate_keywords(self, item):
        """关键词验证，返回评分"""
        score = 0
        content = item.get('content', '').lower()
        title = item.get('title', '').lower()
        combined_text = content + ' ' + title
        
        # 检查非遗类别关键词
        category_matches = sum(1 for cat in self.heritage_keywords['categories'] 
                             if cat in combined_text)
        score += category_matches * 10
        
        # 检查民族特色关键词
        ethnic_matches = sum(1 for ethnic in self.heritage_keywords['ethnic_groups'] 
                           if ethnic in combined_text)
        score += ethnic_matches * 8
        
        # 检查地域标识
        region_matches = sum(1 for region in self.heritage_keywords['regions'] 
                           if region in combined_text)
        score += region_matches * 5
        
        # 检查已知项目
        project_matches = sum(1 for project in self.heritage_keywords['known_projects'] 
                            if project in combined_text)
        score += project_matches * 15
        
        return min(50, score)  # 最高50分
        
    def _validate_source(self, item):
        """来源可信度验证"""
        source = item.get('source', '').lower()
        link = item.get('link', '').lower()
        
        # 政府官方网站得分最高
        if any(gov_indicator in source for gov_indicator in ['政府', 'gov.cn', '文化部', '旅游部']):
            return 30
            
        # 官方文化机构
        if any(culture_indicator in source for culture_indicator in ['文化', '博物馆', '非遗', '遗产']):
            return 20
            
        # 学术机构
        if any(academic_indicator in source for academic_indicator in ['大学', '学院', '研究']):
            return 15
            
        # 新闻媒体
        if any(media_indicator in source for media_indicator in ['新闻', '日报', '电视台']):
            return 10
            
        return 5  # 其他来源基础分
        
    def _identify_heritage_level(self, item):
        """识别非遗项目级别"""
        content = item.get('content', '').lower()
        title = item.get('title', '').lower()
        combined_text = content + ' ' + title
        
        # 检查国家级项目
        for project in self.official_heritage_projects['national_level']:
            if project in combined_text:
                return 'national'
                
        # 检查省级项目
        for project in self.official_heritage_projects['provincial_level']:
            if project in combined_text:
                return 'provincial'
                
        # 根据关键词判断级别
        if '国家级' in combined_text or '国家非物质文化遗产' in combined_text:
            return 'national'
        elif '省级' in combined_text or '贵州省非物质文化遗产' in combined_text:
            return 'provincial'
        elif '州级' in combined_text or '市级' in combined_text or '县级' in combined_text:
            return 'local'
            
        return 'unknown'
        
    def _assess_content_quality(self, item):
        """评估内容质量"""
        score = 0
        content = item.get('content', '')
        
        # 内容长度评分
        if len(content) > 200:
            score += 5
        if len(content) > 500:
            score += 5
            
        # 结构化信息评分
        if '传承人' in content:
            score += 3
        if '保护单位' in content:
            score += 3
        if '申报' in content:
            score += 2
        if '批准' in content:
            score += 2
            
        # 具体描述评分
        descriptive_words = ['技艺', '工艺', '仪式', '表演', '制作', '传统', '历史']
        description_score = sum(1 for word in descriptive_words if word in content)
        score += min(5, description_score)
        
        return score
        
    def _validate_date(self, item):
        """验证日期格式"""
        date_str = item.get('date', '')
        if not date_str:
            return True  # 没有日期不算错误
            
        date_pattern = self.validation_rules['date_format']
        return bool(re.search(date_pattern, date_str))
        
    def _is_duplicate_content(self, item):
        """检查是否为重复内容（简单实现）"""
        # 这里可以实现更复杂的去重逻辑
        # 目前只是基础检查
        content = item.get('content', '')
        title = item.get('title', '')
        
        # 检查是否内容过于简短且重复
        if len(content) < 100 and len(title) < 20:
            return True
            
        return False
        
    def clean_heritage_data(self, heritage_list):
        """清洗非遗数据列表"""
        cleaned_data = []
        
        for item in heritage_list:
            # 验证数据
            validation_result = self.validate_heritage_item(item)
            
            if validation_result['is_valid']:
                # 清洗和标准化数据
                cleaned_item = self._clean_single_item(item)
                cleaned_item['validation_score'] = validation_result['confidence_score']
                cleaned_item['heritage_level'] = validation_result['heritage_level']
                cleaned_data.append(cleaned_item)
            else:
                self.logger.warning(f"数据验证失败: {item.get('title', 'Unknown')} - {validation_result['issues']}")
                
        self.logger.info(f"数据清洗完成: 原始数据 {len(heritage_list)} 条，有效数据 {len(cleaned_data)} 条")
        return cleaned_data
        
    def _clean_single_item(self, item):
        """清洗单个数据项"""
        cleaned_item = item.copy()
        
        # 清理标题
        title = cleaned_item.get('title', '').strip()
        title = re.sub(r'\s+', ' ', title)  # 合并多个空格
        cleaned_item['title'] = title
        
        # 清理内容
        content = cleaned_item.get('content', '').strip()
        content = re.sub(r'\s+', ' ', content)  # 合并多个空格
        content = re.sub(r'[^\w\s\u4e00-\u9fff，。！？；：""''（）【】]', '', content)  # 移除特殊字符
        cleaned_item['content'] = content
        
        # 标准化日期格式
        date_str = cleaned_item.get('date', '')
        if date_str:
            # 尝试标准化日期格式
            date_match = re.search(r'(\d{4})[-/年](\d{1,2})[-/月](\d{1,2})[日]?', date_str)
            if date_match:
                year, month, day = date_match.groups()
                cleaned_item['date'] = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                
        return cleaned_item
