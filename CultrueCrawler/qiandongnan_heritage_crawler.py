#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
贵州省黔东南州非物质文化遗产爬虫程序
从官方权威网站爬取真实、已验证的非物质文化遗产信息
"""

import requests
import time
import json
import csv
import os
import pandas as pd
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import logging
from datetime import datetime
import re
from config import OFFICIAL_SOURCES, COUNTY_SOURCES, HERITAGE_KEYWORDS, CRAWLER_CONFIG, OUTPUT_CONFIG
from data_validator import HeritageDataValidator

class QiandongnanHeritageCrawler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })

        # 设置日志
        self.setup_logging()

        # 加载配置
        self.official_sources = OFFICIAL_SOURCES
        self.county_sources = COUNTY_SOURCES
        self.heritage_keywords = HERITAGE_KEYWORDS
        self.crawler_config = CRAWLER_CONFIG
        self.output_config = OUTPUT_CONFIG

        # 初始化数据验证器
        self.validator = HeritageDataValidator()

        # 存储结果
        self.heritage_data = []
        self.crawled_urls = set()  # 避免重复爬取

    def setup_logging(self):
        """设置日志记录"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_filename = self.output_config['log_filename'].format(timestamp=timestamp)

        logging.basicConfig(
            level=getattr(logging, self.crawler_config['log_level']),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def safe_request(self, url, max_retries=None, delay=None):
        """安全的HTTP请求，包含重试机制"""
        if max_retries is None:
            max_retries = self.crawler_config['max_retries']
        if delay is None:
            delay = self.crawler_config['request_delay']

        # 避免重复爬取
        if url in self.crawled_urls:
            return None

        for attempt in range(max_retries):
            try:
                response = self.session.get(url, timeout=self.crawler_config['timeout'])
                response.raise_for_status()
                response.encoding = 'utf-8'
                self.crawled_urls.add(url)
                return response
            except requests.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {url} - {e}")
                if attempt < max_retries - 1:
                    time.sleep(delay)
                else:
                    self.logger.error(f"最终请求失败: {url}")
                    return None

    def crawl_official_sources(self):
        """爬取官方权威数据源"""
        self.logger.info("开始爬取官方权威数据源...")

        for source_key, source_info in self.official_sources.items():
            self.logger.info(f"正在爬取: {source_info['name']}")

            try:
                # 爬取主要搜索路径
                for search_path in source_info.get('search_paths', []):
                    # 构建搜索URL
                    for keyword in self.heritage_keywords['regions'] + ['非物质文化遗产']:
                        if 'search?q=' in search_path:
                            url = urljoin(source_info['base_url'], search_path + keyword)
                        else:
                            url = urljoin(source_info['base_url'], search_path)

                        response = self.safe_request(url)
                        if response:
                            soup = BeautifulSoup(response.text, 'html.parser')
                            self.parse_heritage_info(soup, source_info['name'], keyword)
                            time.sleep(self.crawler_config['request_delay'])

                        # 限制每个网站的爬取页面数
                        if len(self.crawled_urls) > self.crawler_config['max_pages_per_site']:
                            break

            except Exception as e:
                self.logger.error(f"爬取 {source_info['name']} 时出错: {e}")

    def crawl_county_sources(self):
        """爬取各县市政府网站"""
        self.logger.info("开始爬取各县市政府网站...")

        for county_key, county_info in self.county_sources.items():
            self.logger.info(f"正在爬取: {county_info['name']}")

            try:
                # 常见的非遗相关页面路径
                heritage_paths = [
                    '/zwgk/bmxxgk/whlyj/',  # 文化旅游局
                    '/zwgk/zfxxgk/',  # 政府信息公开
                    '/xwzx/',  # 新闻中心
                    '/lyfw/',  # 旅游服务
                ]

                for path in heritage_paths:
                    url = urljoin(county_info['base_url'], path)
                    response = self.safe_request(url)

                    if response:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        self.parse_heritage_info(soup, county_info['name'], county_key)
                        time.sleep(self.crawler_config['request_delay'])

                        # 查找更多相关链接
                        self.crawl_related_links(soup, county_info['base_url'], county_info['name'])

            except Exception as e:
                self.logger.error(f"爬取 {county_info['name']} 时出错: {e}")

    def crawl_related_links(self, soup, base_url, source_name):
        """爬取页面中的相关链接"""
        try:
            # 查找包含非遗关键词的链接
            links = soup.find_all('a', href=True)
            heritage_links = []

            for link in links:
                link_text = link.get_text(strip=True).lower()
                href = link['href']

                # 检查链接文本是否包含非遗相关关键词
                if any(keyword in link_text for keyword in ['非遗', '文化', '传统', '民俗', '技艺']):
                    full_url = urljoin(base_url, href)
                    heritage_links.append(full_url)

            # 爬取相关链接（限制数量）
            for url in heritage_links[:5]:  # 最多爬取5个相关链接
                response = self.safe_request(url)
                if response:
                    soup = BeautifulSoup(response.text, 'html.parser')
                    self.parse_heritage_info(soup, source_name, 'related_link')
                    time.sleep(self.crawler_config['request_delay'])

        except Exception as e:
            self.logger.warning(f"爬取相关链接时出错: {e}")

    def parse_heritage_info(self, soup, source, keyword):
        """解析网页中的非遗信息"""
        try:
            # 查找包含非遗信息的元素
            heritage_elements = soup.find_all(['div', 'article', 'section', 'li', 'p'],
                                            class_=re.compile(r'(content|article|news|item|list)', re.I))

            # 如果没有找到特定class的元素，尝试查找所有可能的容器
            if not heritage_elements:
                heritage_elements = soup.find_all(['div', 'article', 'section'])

            for element in heritage_elements:
                text = element.get_text(strip=True)

                # 检查文本长度，过短的跳过
                if len(text) < 50:
                    continue

                # 检查是否包含非遗相关关键词
                heritage_keywords = self.heritage_keywords['categories'] + \
                                  self.heritage_keywords['ethnic_groups'] + \
                                  ['非物质文化遗产', '非遗', '传统', '民俗', '文化遗产']

                # 检查是否包含地域关键词
                region_keywords = self.heritage_keywords['regions']

                has_heritage_keyword = any(kw in text for kw in heritage_keywords)
                has_region_keyword = any(kw in text for kw in region_keywords)

                if has_heritage_keyword and has_region_keyword:
                    # 提取标题
                    title_elem = element.find(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'title', 'a'])
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                    else:
                        # 如果没有找到标题元素，使用文本的前50个字符作为标题
                        title = text[:50] + '...' if len(text) > 50 else text

                    # 提取链接
                    link_elem = element.find('a', href=True)
                    if link_elem:
                        link = urljoin(source, link_elem['href']) if link_elem['href'].startswith('/') else link_elem['href']
                    else:
                        link = ''

                    # 提取时间
                    date_pattern = r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?'
                    date_match = re.search(date_pattern, text)
                    date = date_match.group() if date_match else ''

                    heritage_item = {
                        'title': title,
                        'content': text[:1000] + '...' if len(text) > 1000 else text,
                        'source': source,
                        'keyword': keyword,
                        'link': link,
                        'date': date,
                        'crawl_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    self.heritage_data.append(heritage_item)
                    self.logger.info(f"发现非遗信息: {title[:50]}...")

        except Exception as e:
            self.logger.error(f"解析页面时出错: {e}")

    def save_to_csv(self, filename='qiandongnan_heritage.csv'):
        """保存数据到CSV文件"""
        if not self.heritage_data:
            self.logger.warning("没有数据可保存")
            return

        fieldnames = ['title', 'content', 'source', 'keyword', 'link', 'date', 'crawl_time',
                     'validation_score', 'heritage_level']

        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.heritage_data)

        self.logger.info(f"数据已保存到 {filename}, 共 {len(self.heritage_data)} 条记录")

    def save_to_json(self, filename='qiandongnan_heritage.json'):
        """保存数据到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(self.heritage_data, jsonfile, ensure_ascii=False, indent=2)

        self.logger.info(f"数据已保存到 {filename}")

    def save_to_excel(self, filename='qiandongnan_heritage.xlsx'):
        """保存数据到Excel文件"""
        if not self.heritage_data:
            self.logger.warning("没有数据可保存")
            return

        try:
            df = pd.DataFrame(self.heritage_data)
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='非遗数据', index=False)

                # 创建统计表
                stats_data = self.get_statistics_data()
                stats_df = pd.DataFrame(stats_data)
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)

            self.logger.info(f"数据已保存到 {filename}")
        except Exception as e:
            self.logger.error(f"保存Excel文件时出错: {e}")

    def get_statistics_data(self):
        """获取统计数据"""
        stats = []

        # 按来源统计
        sources = {}
        heritage_levels = {}

        for item in self.heritage_data:
            source = item.get('source', 'Unknown')
            sources[source] = sources.get(source, 0) + 1

            level = item.get('heritage_level', 'unknown')
            heritage_levels[level] = heritage_levels.get(level, 0) + 1

        # 来源统计
        for source, count in sources.items():
            stats.append({'类型': '数据来源', '项目': source, '数量': count})

        # 级别统计
        for level, count in heritage_levels.items():
            level_name = {
                'national': '国家级',
                'provincial': '省级',
                'local': '地方级',
                'unknown': '未知级别'
            }.get(level, level)
            stats.append({'类型': '遗产级别', '项目': level_name, '数量': count})

        return stats

    def generate_report(self, filename='report.txt'):
        """生成爬取报告"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("黔东南州非物质文化遗产爬取报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"爬取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总计数据: {len(self.heritage_data)} 条\n\n")

                # 数据来源统计
                sources = {}
                for item in self.heritage_data:
                    source = item.get('source', 'Unknown')
                    sources[source] = sources.get(source, 0) + 1

                f.write("数据来源统计:\n")
                for source, count in sources.items():
                    f.write(f"  {source}: {count} 条\n")
                f.write("\n")

                # 遗产级别统计
                levels = {}
                for item in self.heritage_data:
                    level = item.get('heritage_level', 'unknown')
                    levels[level] = levels.get(level, 0) + 1

                f.write("遗产级别统计:\n")
                for level, count in levels.items():
                    level_name = {
                        'national': '国家级',
                        'provincial': '省级',
                        'local': '地方级',
                        'unknown': '未知级别'
                    }.get(level, level)
                    f.write(f"  {level_name}: {count} 条\n")

            self.logger.info(f"报告已生成: {filename}")
        except Exception as e:
            self.logger.error(f"生成报告时出错: {e}")

if __name__ == "__main__":
    crawler = QiandongnanHeritageCrawler()
    # 运行官方数据源爬取
    crawler.crawl_official_sources()
    # 运行县市数据源爬取
    crawler.crawl_county_sources()

    # 数据验证和清洗
    if crawler.heritage_data:
        cleaned_data = crawler.validator.clean_heritage_data(crawler.heritage_data)
        crawler.heritage_data = cleaned_data

        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        crawler.save_to_csv(f'qiandongnan_heritage_{timestamp}.csv')
        crawler.save_to_json(f'qiandongnan_heritage_{timestamp}.json')
        crawler.save_to_excel(f'qiandongnan_heritage_{timestamp}.xlsx')
        crawler.generate_report(f'report_{timestamp}.txt')

        print(f"爬取完成！共获取 {len(crawler.heritage_data)} 条有效数据")
